#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽修复
"""

import sys
import os
sys.path.append('.')

def test_folder_drag_logic():
    """测试文件夹拖拽逻辑"""
    print("测试文件夹拖拽逻辑...")
    
    # 模拟文件夹信息
    folder_info = {
        'id': 0,  # 文件夹的ID为0
        'name': 'test_folder',
        'path': '/path/to/test_folder',
        'category': '人物',
        'is_folder': True
    }
    
    # 检查是否为文件夹
    is_folder = folder_info.get('is_folder', False)
    file_id = folder_info.get('id')
    
    print(f"文件夹信息: {folder_info['name']}")
    print(f"是否为文件夹: {is_folder}")
    print(f"文件ID: {file_id}")
    
    # 测试拖拽逻辑
    if is_folder and file_id == 0:
        print("✅ 文件夹拖拽逻辑正确 - 会使用文件系统操作")
    elif file_id and file_id != 0:
        print("✅ 普通文件拖拽逻辑正确 - 会使用数据库操作")
    else:
        print("❌ 拖拽逻辑有问题")
    
    print("测试完成！")

if __name__ == "__main__":
    test_folder_drag_logic()
