# -*- coding: utf-8 -*-
"""
分类列表 - 左侧分类导航组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QListWidget, QListWidgetItem,
                            QLabel, QFrame, QHBoxLayout, QPushButton, QMenu, QInputDialog, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QDragEnterEvent, QDropEvent, QContextMenuEvent

from models.filesystem_manager import FileSystemManager
from utils.config_manager import ConfigManager


class CategoryListWidget(QListWidget):
    """支持拖放的分类列表组件"""

    file_dropped = pyqtSignal(str, str)  # 文件拖放信号：文件信息，目标分类
    category_color_change_requested = pyqtSignal(str)  # 分类颜色更改请求信号
    new_category_requested = pyqtSignal()  # 新建分类请求信号
    delete_category_requested = pyqtSignal(str)  # 删除分类请求信号

    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setDragDropMode(QListWidget.DragDropMode.DropOnly)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event is None:
            return
        mime_data = event.mimeData()
        if mime_data and mime_data.hasFormat("application/x-file-info"):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if event is None:
            return
        mime_data = event.mimeData()
        if mime_data and mime_data.hasFormat("application/x-file-info"):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖放事件"""
        if event is None:
            return
        mime_data = event.mimeData()
        if mime_data and mime_data.hasFormat("application/x-file-info"):
            # 获取拖放位置的分类
            item = self.itemAt(event.position().toPoint())
            if item:
                target_category = item.text()
                # 去掉颜色前缀
                if target_category.startswith("● "):
                    target_category = target_category[2:]

                # 获取文件数据并发送信号
                file_data = mime_data.data("application/x-file-info").data()
                self.file_dropped.emit(file_data.decode('utf-8'), target_category)
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        menu = QMenu(self)

        if item:
            # 右键点击在分类上
            category = item.text()
            # 去掉颜色前缀
            if category.startswith("● "):
                category = category[2:]

            # 更改颜色
            change_color_action = menu.addAction("🎨 更改颜色")
            change_color_action.triggered.connect(
                lambda: self.category_color_change_requested.emit(category)
            )

            menu.addSeparator()

            # 删除分类（不能删除默认分类）
            default_categories = ["人物", "场景", "道具", "其他", "回收站"]
            if category not in default_categories:
                delete_action = menu.addAction("🗑️ 删除分类")
                delete_action.triggered.connect(
                    lambda: self.delete_category_requested.emit(category)
                )

        # 新建分类（无论点击哪里都可以新建）
        if item:
            menu.addSeparator()
        new_category_action = menu.addAction("➕ 新建分类")
        new_category_action.triggered.connect(self.new_category_requested.emit)

        # 显示菜单
        menu.exec(self.mapToGlobal(position))


class CategoryList(QWidget):
    """分类列表组件"""
    
    # 信号
    category_changed = pyqtSignal(str)  # 分类改变信号
    file_dropped = pyqtSignal(str, str)  # 文件拖拽信号 (file_data, target_category)
    statusbar_message = pyqtSignal(str) # 状态栏消息信号
    
    def __init__(self, config_manager: ConfigManager, fs_manager: FileSystemManager):
        super().__init__()
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.current_category = "人物"  # 默认选中人物分类
        
        self.init_ui()
        self.load_categories()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("分类")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 分类列表
        self.category_list = CategoryListWidget()
        self.category_list.setAlternatingRowColors(True)
        self.category_list.itemClicked.connect(self.on_category_clicked)
        self.category_list.file_dropped.connect(self.on_file_dropped)
        self.category_list.category_color_change_requested.connect(self.on_category_color_change_requested)
        self.category_list.new_category_requested.connect(self.create_new_category)
        self.category_list.delete_category_requested.connect(self.delete_category)
        layout.addWidget(self.category_list)
        
        # 新建分类按钮
        new_category_btn = QPushButton("➕ 新建分类")
        new_category_btn.clicked.connect(self.create_new_category)
        layout.addWidget(new_category_btn)

        # 应用样式
        self.apply_styles()
    
    def create_new_category(self):
        """创建新分类"""
        category_name, ok = QInputDialog.getText(self, "新建分类", "请输入分类名称:")
        if ok and category_name:
            try:
                self.config_manager.add_category(category_name)
                self.refresh_categories()
                self.statusbar_message.emit(f"成功创建分类: {category_name}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建分类失败: {str(e)}")

    def delete_category(self, category_name: str):
        """删除分类"""
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除分类 '{category_name}' 吗？\n\n注意：该分类下的所有文件将被移动到回收站。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 获取该分类下的所有文件
                category_files = self.fs_manager.get_files_by_category(category_name)

                # 将文件移动到回收站
                from utils.file_operations import FileOperations
                file_operations = FileOperations(self.config_manager, self.fs_manager)

                for file_info in category_files:
                    file_id = file_info.get('id')
                    if file_id:
                        file_operations.delete_file(file_id)

                # 删除分类
                self.config_manager.remove_category(category_name)
                self.refresh_categories()

                # 如果删除的是当前分类，切换到默认分类
                if self.current_category == category_name:
                    self.set_current_category("人物", emit_signal=True)

                self.statusbar_message.emit(f"成功删除分类: {category_name}")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除分类失败: {e}")

    def apply_styles(self):
        """应用样式表"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            padding: 5px;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            outline: none;
        }
        
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #4a4a4a;
        }
        
        QListWidget::item:selected {
            background-color: #5a5a5a;
        }
        
        QListWidget::item:hover {
            background-color: #4a4a4a;
        }
        
        QFrame {
            color: #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def load_categories(self):
        """加载分类列表"""
        self.category_list.clear()
        categories = self.config_manager.colors["categories"].keys()
        
        for category in categories:
            item = QListWidgetItem(category)
            
            # 设置分类颜色
            color = self.config_manager.get_category_color(category)
            item.setData(Qt.ItemDataRole.UserRole, color)
            
            # 设置图标颜色（使用彩色圆点表示）
            self.set_item_color(item, color)
            
            self.category_list.addItem(item)
        
        # 默认选中第一个分类
        if self.category_list.count() > 0:
            self.category_list.setCurrentRow(0)
            self.category_changed.emit(self.current_category)
    
    def set_item_color(self, item: QListWidgetItem, color: str):
        """设置列表项颜色"""
        # 获取原始分类名称（去掉可能的颜色前缀）
        category_name = item.text()
        if category_name.startswith("● "):
            category_name = category_name[2:]

        # 设置带颜色圆点的文本
        item.setText(f"● {category_name}")

        # 设置文本颜色
        qcolor = QColor(color)
        item.setForeground(qcolor)

        # 设置字体加粗
        font = item.font()
        font.setBold(True)
        item.setFont(font)
    
    def on_category_clicked(self, item: QListWidgetItem):
        """分类点击事件"""
        category = item.text()
        # 去掉颜色前缀
        if category.startswith("● "):
            category = category[2:]

        if category != self.current_category:
            self.current_category = category
            self.category_changed.emit(category)

    def on_file_dropped(self, file_data: str, target_category: str):
        """文件拖放到分类的处理"""
        # 切换到目标分类
        self.set_current_category(target_category, emit_signal=True)

        # 发送信号通知文件移动
        # 这里需要添加一个新的信号来处理文件移动
        print(f"文件拖放到分类: {target_category}，已切换到该分类")

    def get_current_category(self) -> str:
        """获取当前选中的分类"""
        return self.current_category
    
    def set_category_color(self, category: str, color: str):
        """设置分类颜色"""
        self.config_manager.set_category_color(category, color)

        # 更新列表项颜色
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            if item:
                item_text = item.text()
                # 去掉颜色前缀进行比较
                if item_text.startswith("● "):
                    item_text = item_text[2:]

                if item_text == category:
                    item.setData(Qt.ItemDataRole.UserRole, color)
                    self.set_item_color(item, color)
                    break
    
    def refresh_categories(self):
        """刷新分类列表"""
        self.category_list.clear()
        self.load_categories()

    def set_current_category(self, category: str, emit_signal: bool = True):
        """设置当前选中的分类"""
        for i in range(self.category_list.count()):
            item = self.category_list.item(i)
            if item:
                item_text = item.text()
                # 去掉颜色前缀进行比较
                if item_text.startswith("● "):
                    item_text = item_text[2:]

                if item_text == category:
                    self.category_list.setCurrentRow(i)
                    self.current_category = category
                    if emit_signal:
                        self.category_changed.emit(category)
                    break

    def on_category_color_change_requested(self, category: str):
        """处理分类颜色更改请求"""
        from ui.color_dialog import ColorDialog

        color_dialog = ColorDialog(
            self.config_manager, self.fs_manager,
            "category", [], category, self
        )
        color_dialog.color_changed.connect(self.on_category_color_changed)
        color_dialog.exec()

    def on_category_color_changed(self, color: str):
        """分类颜色改变处理"""
        # 刷新分类列表以显示新颜色
        self.refresh_categories()
