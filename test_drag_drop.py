#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽功能的简单脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """测试导入是否正常"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from src.ui.main_window import MainWindow
        from src.utils.config_manager import ConfigManager
        from src.models.filesystem_manager import FileSystemManager
        print("✓ 所有导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_drag_drop_manager():
    """测试拖拽管理器"""
    try:
        from src.utils.config_manager import ConfigManager
        from src.models.filesystem_manager import FileSystemManager
        from src.utils.drag_drop import DragDropManager
        
        config_manager = ConfigManager()
        storage_path = config_manager.get_storage_path()
        fs_manager = FileSystemManager(str(storage_path))
        drag_drop_manager = DragDropManager(config_manager, fs_manager)
        
        print("✓ 拖拽管理器创建成功")
        return True
    except Exception as e:
        print(f"✗ 拖拽管理器创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试拖拽功能...")
    
    # 测试导入
    if not test_imports():
        return False
    
    # 测试拖拽管理器
    if not test_drag_drop_manager():
        return False
    
    print("✓ 所有测试通过")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
